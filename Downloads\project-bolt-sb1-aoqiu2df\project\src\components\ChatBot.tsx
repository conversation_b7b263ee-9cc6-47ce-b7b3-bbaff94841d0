import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Mic, Mic<PERSON>ff, Volume2, Image, Calculator } from 'lucide-react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  type?: 'text' | 'image' | 'equation';
}

export const ChatBot: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Hello! I\'m your AI tutor. I can help you with math, physics, chemistry, and more. What would you like to learn today?',
      sender: 'bot',
      timestamp: new Date(),
    }
  ]);
  
  const [inputValue, setInputValue] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const simulateAIResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('math') || lowerMessage.includes('equation') || lowerMessage.includes('calculate')) {
      return 'I can help you with mathematical problems! Whether it\'s algebra, calculus, geometry, or statistics, I\'m here to guide you through step-by-step solutions. What specific math topic would you like to explore?';
    }
    
    if (lowerMessage.includes('physics')) {
      return 'Physics is fascinating! I can explain concepts from classical mechanics to quantum physics. Topics like motion, energy, waves, electricity, and magnetism are my specialty. What physics concept interests you?';
    }
    
    if (lowerMessage.includes('chemistry')) {
      return 'Chemistry is all about understanding matter and its interactions! I can help with atomic structure, chemical bonding, reactions, thermodynamics, and more. What chemistry topic would you like to dive into?';
    }
    
    if (lowerMessage.includes('derivative') || lowerMessage.includes('integral')) {
      return 'Calculus is a powerful tool! For derivatives, remember the power rule: d/dx(x^n) = nx^(n-1). For integrals, it\'s the reverse. Would you like me to walk through a specific problem with you?';
    }
    
    return 'That\'s a great question! I\'m analyzing your query using advanced NLP to provide the most relevant explanation. Based on your learning style and progress, I recommend breaking this down into smaller concepts. Can you tell me more about what specifically you\'d like to understand?';
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI processing time
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: simulateAIResponse(inputValue),
        sender: 'bot',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, botResponse]);
      setIsLoading(false);
    }, 1000 + Math.random() * 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleListening = () => {
    setIsListening(!isListening);
    // Simulate speech recognition
    if (!isListening) {
      setTimeout(() => {
        setInputValue('What is the derivative of x squared?');
        setIsListening(false);
      }, 3000);
    }
  };

  const speakMessage = (content: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(content);
      utterance.rate = 0.9;
      utterance.pitch = 1;
      speechSynthesis.speak(utterance);
    }
  };

  const quickActions = [
    { label: 'Solve Equation', icon: Calculator, prompt: 'Help me solve a mathematical equation' },
    { label: 'Explain Concept', icon: Bot, prompt: 'Explain a concept in simple terms' },
    { label: 'Practice Problems', icon: Image, prompt: 'Give me practice problems to solve' },
  ];

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="bg-white/10 backdrop-blur-md border-b border-white/20 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-white font-bold">AI Tutor Assistant</h1>
              <p className="text-white/60 text-sm">Powered by Advanced NLP & Machine Learning</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2 bg-white/10 rounded-full px-3 py-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white text-sm">Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex items-start space-x-3 ${
              message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''
            }`}
          >
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              message.sender === 'user' 
                ? 'bg-gradient-to-r from-green-400 to-blue-400' 
                : 'bg-gradient-to-r from-purple-400 to-blue-400'
            }`}>
              {message.sender === 'user' ? (
                <User className="w-4 h-4 text-white" />
              ) : (
                <Bot className="w-4 h-4 text-white" />
              )}
            </div>
            
            <div className={`max-w-xs lg:max-w-md xl:max-w-lg ${
              message.sender === 'user' ? 'text-right' : ''
            }`}>
              <div className={`inline-block p-4 rounded-2xl ${
                message.sender === 'user'
                  ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white'
                  : 'bg-white/10 backdrop-blur-md border border-white/20 text-white'
              }`}>
                <p className="text-sm leading-relaxed">{message.content}</p>
              </div>
              
              <div className="flex items-center mt-2 space-x-2">
                <p className="text-xs text-white/60">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
                {message.sender === 'bot' && (
                  <button
                    onClick={() => speakMessage(message.content)}
                    className="text-white/60 hover:text-white transition-colors"
                  >
                    <Volume2 className="w-3 h-3" />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-4">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-t border-white/20">
        <div className="flex space-x-2 mb-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={() => setInputValue(action.prompt)}
              className="flex items-center space-x-2 bg-white/10 hover:bg-white/20 rounded-lg px-3 py-2 text-white text-sm transition-colors"
            >
              <action.icon className="w-4 h-4" />
              <span>{action.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Input */}
      <div className="p-4 bg-white/10 backdrop-blur-md border-t border-white/20">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your studies..."
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/60 resize-none focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent"
              rows={1}
            />
            {isListening && (
              <div className="absolute inset-0 bg-red-500/20 rounded-xl flex items-center justify-center">
                <div className="flex items-center space-x-2 text-red-400">
                  <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">Listening...</span>
                </div>
              </div>
            )}
          </div>
          
          <button
            onClick={toggleListening}
            className={`p-3 rounded-xl transition-colors ${
              isListening 
                ? 'bg-red-500 hover:bg-red-600 text-white' 
                : 'bg-white/10 hover:bg-white/20 text-white'
            }`}
          >
            {isListening ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
          </button>
          
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-white p-3 rounded-xl transition-all duration-200"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};