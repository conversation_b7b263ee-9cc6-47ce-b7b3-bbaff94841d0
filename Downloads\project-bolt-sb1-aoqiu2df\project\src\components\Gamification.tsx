import React, { useState } from 'react';
import { 
  Trophy, 
  Star, 
  Target, 
  Crown, 
  Medal, 
  Zap,
  TrendingUp,
  Calendar,
  Users,
  Gift,
  Flame,
  Award
} from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  progress: number;
  maxProgress: number;
  isCompleted: boolean;
  xpReward: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface LeaderboardEntry {
  rank: number;
  name: string;
  xp: number;
  level: number;
  avatar: string;
  isCurrentUser?: boolean;
}

export const Gamification: React.FC<{ user: any }> = ({ user }) => {
  const [activeTab, setActiveTab] = useState<'achievements' | 'leaderboard' | 'rewards'>('achievements');

  const achievements: Achievement[] = [
    {
      id: '1',
      title: 'First Steps',
      description: 'Complete your first lesson',
      icon: '🎯',
      progress: 1,
      maxProgress: 1,
      isCompleted: true,
      xpReward: 50,
      rarity: 'common'
    },
    {
      id: '2',
      title: 'Streak Master',
      description: 'Maintain a 7-day learning streak',
      icon: '🔥',
      progress: 7,
      maxProgress: 7,
      isCompleted: true,
      xpReward: 200,
      rarity: 'rare'
    },
    {
      id: '3',
      title: 'Math Wizard',
      description: 'Solve 100 math problems',
      icon: '🧮',
      progress: 67,
      maxProgress: 100,
      isCompleted: false,
      xpReward: 300,
      rarity: 'epic'
    },
    {
      id: '4',
      title: 'Voice Learner',
      description: 'Complete 10 voice learning sessions',
      icon: '🎙️',
      progress: 10,
      maxProgress: 10,
      isCompleted: true,
      xpReward: 150,
      rarity: 'rare'
    },
    {
      id: '5',
      title: 'AI Whisperer',
      description: 'Have 50 conversations with the AI tutor',
      icon: '🤖',
      progress: 32,
      maxProgress: 50,
      isCompleted: false,
      xpReward: 250,
      rarity: 'epic'
    },
    {
      id: '6',
      title: 'Speed Demon',
      description: 'Complete a lesson in under 5 minutes',
      icon: '⚡',
      progress: 0,
      maxProgress: 1,
      isCompleted: false,
      xpReward: 100,
      rarity: 'common'
    },
    {
      id: '7',
      title: 'Knowledge Seeker',
      description: 'Complete 5 different subject modules',
      icon: '📚',
      progress: 3,
      maxProgress: 5,
      isCompleted: false,
      xpReward: 500,
      rarity: 'legendary'
    },
    {
      id: '8',
      title: 'Perfect Score',
      description: 'Get 100% on any quiz',
      icon: '💯',
      progress: 1,
      maxProgress: 1,
      isCompleted: true,
      xpReward: 200,
      rarity: 'rare'
    }
  ];

  const leaderboard: LeaderboardEntry[] = [
    { rank: 1, name: 'Emma Watson', xp: 5420, level: 8, avatar: '👩‍🎓' },
    { rank: 2, name: 'Michael Chen', xp: 4850, level: 7, avatar: '👨‍💻' },
    { rank: 3, name: 'Sarah Johnson', xp: 4200, level: 6, avatar: '👩‍🔬' },
    { rank: 4, name: 'Alex Johnson', xp: 2340, level: 5, avatar: '👨‍🎓', isCurrentUser: true },
    { rank: 5, name: 'David Kim', xp: 2180, level: 4, avatar: '👨‍🎨' },
    { rank: 6, name: 'Lisa Zhang', xp: 1950, level: 4, avatar: '👩‍💼' },
    { rank: 7, name: 'James Wilson', xp: 1720, level: 3, avatar: '👨‍🔬' },
    { rank: 8, name: 'Anna Rodriguez', xp: 1650, level: 3, avatar: '👩‍🎯' },
  ];

  const rewards = [
    { id: '1', title: 'Custom Avatar', cost: 500, icon: '🎭', description: 'Personalize your profile' },
    { id: '2', title: 'Dark Theme', cost: 300, icon: '🌙', description: 'Unlock dark mode' },
    { id: '3', title: 'Extra Hints', cost: 200, icon: '💡', description: '5 additional hints per day' },
    { id: '4', title: 'Priority Support', cost: 1000, icon: '🎯', description: 'Get faster help responses' },
    { id: '5', title: 'Certificate Badge', cost: 800, icon: '🏆', description: 'Show your achievements' },
    { id: '6', title: 'Advanced AI Features', cost: 1500, icon: '🤖', description: 'Unlock premium AI tools' },
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'from-gray-400 to-gray-600';
      case 'rare':
        return 'from-blue-400 to-blue-600';
      case 'epic':
        return 'from-purple-400 to-purple-600';
      case 'legendary':
        return 'from-yellow-400 to-orange-500';
      default:
        return 'from-gray-400 to-gray-600';
    }
  };

  const getRarityBorder = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-400/50';
      case 'rare':
        return 'border-blue-400/50';
      case 'epic':
        return 'border-purple-400/50';
      case 'legendary':
        return 'border-yellow-400/50';
      default:
        return 'border-gray-400/50';
    }
  };

  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-4 flex items-center">
          <Trophy className="w-8 h-8 mr-3 text-yellow-400" />
          Achievements & Rewards
        </h1>
        <p className="text-white/70 text-lg">
          Track your progress and unlock rewards as you learn
        </p>
      </div>

      {/* User Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-gradient-to-r from-yellow-400/20 to-orange-400/20 backdrop-blur-md rounded-xl p-4 border border-yellow-400/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-400 text-sm font-medium">Current Level</p>
              <p className="text-2xl font-bold text-white">{user.level}</p>
            </div>
            <Crown className="w-8 h-8 text-yellow-400" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-400/20 to-blue-400/20 backdrop-blur-md rounded-xl p-4 border border-purple-400/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-400 text-sm font-medium">XP Points</p>
              <p className="text-2xl font-bold text-white">{user.xp.toLocaleString()}</p>
            </div>
            <Star className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-orange-400/20 to-red-400/20 backdrop-blur-md rounded-xl p-4 border border-orange-400/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-400 text-sm font-medium">Streak</p>
              <p className="text-2xl font-bold text-white">{user.streak} days</p>
            </div>
            <Flame className="w-8 h-8 text-orange-400" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-400/20 to-blue-400/20 backdrop-blur-md rounded-xl p-4 border border-green-400/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-400 text-sm font-medium">Achievements</p>
              <p className="text-2xl font-bold text-white">
                {achievements.filter(a => a.isCompleted).length}/{achievements.length}
              </p>
            </div>
            <Medal className="w-8 h-8 text-green-400" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-white/10 rounded-xl p-1">
        {[
          { id: 'achievements', label: 'Achievements', icon: Trophy },
          { id: 'leaderboard', label: 'Leaderboard', icon: Users },
          { id: 'rewards', label: 'Rewards', icon: Gift }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            <tab.icon className="w-5 h-5" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      {activeTab === 'achievements' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {achievements.map((achievement) => (
            <div
              key={achievement.id}
              className={`bg-white/10 backdrop-blur-md rounded-xl p-6 border transition-all duration-300 hover:bg-white/20 ${
                achievement.isCompleted 
                  ? `${getRarityBorder(achievement.rarity)} shadow-lg` 
                  : 'border-white/20'
              }`}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="text-3xl">{achievement.icon}</div>
                {achievement.isCompleted && (
                  <div className={`px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getRarityColor(achievement.rarity)} text-white`}>
                    {achievement.rarity.toUpperCase()}
                  </div>
                )}
              </div>

              <h3 className="text-lg font-bold text-white mb-2">{achievement.title}</h3>
              <p className="text-white/70 text-sm mb-4">{achievement.description}</p>

              {!achievement.isCompleted && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/60 text-sm">Progress</span>
                    <span className="text-white text-sm font-medium">
                      {achievement.progress}/{achievement.maxProgress}
                    </span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className={`bg-gradient-to-r ${getRarityColor(achievement.rarity)} h-2 rounded-full transition-all duration-300`}
                      style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 font-medium text-sm">+{achievement.xpReward} XP</span>
                </div>
                {achievement.isCompleted && (
                  <div className="flex items-center space-x-1 text-green-400">
                    <Trophy className="w-4 h-4" />
                    <span className="text-sm font-medium">Completed</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'leaderboard' && (
        <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 overflow-hidden">
          <div className="p-6 border-b border-white/20">
            <h2 className="text-xl font-bold text-white flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Global Leaderboard
            </h2>
            <p className="text-white/60 text-sm mt-1">See how you rank against other learners</p>
          </div>

          <div className="divide-y divide-white/20">
            {leaderboard.map((entry) => (
              <div
                key={entry.rank}
                className={`p-4 flex items-center space-x-4 transition-colors ${
                  entry.isCurrentUser ? 'bg-purple-500/20' : 'hover:bg-white/10'
                }`}
              >
                <div className="flex items-center justify-center w-8 h-8">
                  {entry.rank <= 3 ? (
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      entry.rank === 1 ? 'bg-yellow-400' :
                      entry.rank === 2 ? 'bg-gray-400' :
                      'bg-orange-400'
                    }`}>
                      <span className="text-xs font-bold text-white">{entry.rank}</span>
                    </div>
                  ) : (
                    <span className="text-white/60 font-medium">{entry.rank}</span>
                  )}
                </div>

                <div className="text-2xl">{entry.avatar}</div>

                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="text-white font-medium">{entry.name}</p>
                    {entry.isCurrentUser && (
                      <span className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full">You</span>
                    )}
                  </div>
                  <p className="text-white/60 text-sm">Level {entry.level}</p>
                </div>

                <div className="text-right">
                  <p className="text-white font-bold">{entry.xp.toLocaleString()}</p>
                  <p className="text-white/60 text-sm">XP</p>
                </div>

                {entry.rank <= 3 && (
                  <div className="ml-2">
                    <Crown className={`w-5 h-5 ${
                      entry.rank === 1 ? 'text-yellow-400' :
                      entry.rank === 2 ? 'text-gray-400' :
                      'text-orange-400'
                    }`} />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'rewards' && (
        <div>
          <div className="mb-6 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl p-4 border border-purple-400/20">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-bold text-white">Your XP Balance</h2>
                <p className="text-white/70 text-sm">Earn XP by completing lessons and achievements</p>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="w-6 h-6 text-yellow-400" />
                <span className="text-2xl font-bold text-white">{user.xp.toLocaleString()}</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {rewards.map((reward) => (
              <div
                key={reward.id}
                className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              >
                <div className="text-center mb-4">
                  <div className="text-4xl mb-2">{reward.icon}</div>
                  <h3 className="text-lg font-bold text-white mb-2">{reward.title}</h3>
                  <p className="text-white/70 text-sm">{reward.description}</p>
                </div>

                <div className="border-t border-white/20 pt-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Zap className="w-4 h-4 text-yellow-400" />
                      <span className="text-yellow-400 font-medium">{reward.cost} XP</span>
                    </div>
                    <span className={`text-sm ${
                      user.xp >= reward.cost ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {user.xp >= reward.cost ? 'Available' : 'Locked'}
                    </span>
                  </div>

                  <button
                    disabled={user.xp < reward.cost}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                      user.xp >= reward.cost
                        ? 'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white'
                        : 'bg-white/10 text-white/50 cursor-not-allowed'
                    }`}
                  >
                    {user.xp >= reward.cost ? 'Unlock' : 'Insufficient XP'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};