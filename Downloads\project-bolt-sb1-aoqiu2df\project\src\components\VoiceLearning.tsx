import React, { useState, useEffect } from 'react';
import { Mic, MicOff, Volume2, VolumeX, Play, Pause, Ski<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Headphones, AudioWaveform as Waveform, Settings, BookOpen } from 'lucide-react';

interface VoiceSession {
  id: string;
  title: string;
  subject: string;
  duration: string;
  progress: number;
  isActive: boolean;
}

export const VoiceLearning: React.FC = () => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentSession, setCurrentSession] = useState<VoiceSession | null>(null);
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);
  const [volume, setVolume] = useState(0.8);
  const [voiceSettings, setVoiceSettings] = useState({
    voice: 'female',
    language: 'en-US',
    pitch: 1.0,
    rate: 1.0
  });

  const voiceSessions: VoiceSession[] = [
    {
      id: '1',
      title: 'Derivatives and Chain Rule',
      subject: 'Calculus',
      duration: '25 min',
      progress: 60,
      isActive: true
    },
    {
      id: '2',
      title: 'Quantum Superposition',
      subject: 'Physics',
      duration: '18 min',
      progress: 0,
      isActive: false
    },
    {
      id: '3',
      title: 'Organic Reactions',
      subject: 'Chemistry',
      duration: '22 min',
      progress: 100,
      isActive: false
    },
    {
      id: '4',
      title: 'Matrix Operations',
      subject: 'Linear Algebra',
      duration: '30 min',
      progress: 35,
      isActive: false
    }
  ];

  const [audioWaveform, setAudioWaveform] = useState<number[]>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (isSpeaking || isListening) {
        setAudioWaveform(
          Array.from({ length: 20 }, () => Math.random() * 100)
        );
      } else {
        setAudioWaveform(Array.from({ length: 20 }, () => 0));
      }
    }, 100);

    return () => clearInterval(interval);
  }, [isSpeaking, isListening]);

  const startListening = () => {
    setIsListening(true);
    // Simulate speech recognition
    setTimeout(() => {
      setIsListening(false);
      simulateResponse();
    }, 3000);
  };

  const simulateResponse = () => {
    setIsSpeaking(true);
    setTimeout(() => {
      setIsSpeaking(false);
    }, 4000);
  };

  const startSession = (session: VoiceSession) => {
    setCurrentSession(session);
    setIsSpeaking(true);
  };

  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-4 flex items-center">
          <Headphones className="w-8 h-8 mr-3" />
          Voice-Powered Learning
        </h1>
        <p className="text-white/70 text-lg">
          Learn through conversations with AI using advanced Speech Recognition and Text-to-Speech
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Voice Interface */}
        <div className="lg:col-span-2 space-y-6">
          {/* Active Session */}
          {currentSession ? (
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold text-white">{currentSession.title}</h2>
                  <p className="text-white/60">{currentSession.subject}</p>
                </div>
                <button
                  onClick={() => setCurrentSession(null)}
                  className="text-white/60 hover:text-white transition-colors"
                >
                  <VolumeX className="w-5 h-5" />
                </button>
              </div>

              {/* Progress */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/60 text-sm">Progress</span>
                  <span className="text-white text-sm">{currentSession.progress}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${currentSession.progress}%` }}
                  />
                </div>
              </div>

              {/* Playback Controls */}
              <div className="flex items-center justify-center space-x-4 mb-6">
                <button className="p-3 rounded-full bg-white/10 hover:bg-white/20 text-white transition-colors">
                  <SkipBack className="w-6 h-6" />
                </button>
                <button
                  onClick={() => setIsSpeaking(!isSpeaking)}
                  className="p-4 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white transition-all duration-200"
                >
                  {isSpeaking ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                </button>
                <button className="p-3 rounded-full bg-white/10 hover:bg-white/20 text-white transition-colors">
                  <SkipForward className="w-6 h-6" />
                </button>
              </div>

              {/* Speed Control */}
              <div className="flex items-center justify-center space-x-4 mb-6">
                <span className="text-white/60 text-sm">Speed:</span>
                {[0.5, 0.75, 1.0, 1.25, 1.5].map((speed) => (
                  <button
                    key={speed}
                    onClick={() => setPlaybackSpeed(speed)}
                    className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                      playbackSpeed === speed
                        ? 'bg-purple-500 text-white'
                        : 'bg-white/10 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    {speed}x
                  </button>
                ))}
              </div>
            </div>
          ) : (
            /* Voice Interaction Interface */
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 border border-white/20 text-center">
              <div className="mb-8">
                <div className={`w-32 h-32 mx-auto rounded-full bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center mb-4 transition-all duration-300 ${
                  isListening ? 'animate-pulse scale-110' : isSpeaking ? 'animate-bounce' : ''
                }`}>
                  {isListening ? (
                    <Mic className="w-16 h-16 text-white" />
                  ) : isSpeaking ? (
                    <Volume2 className="w-16 h-16 text-white" />
                  ) : (
                    <Headphones className="w-16 h-16 text-white" />
                  )}
                </div>

                <h2 className="text-2xl font-bold text-white mb-2">
                  {isListening ? 'Listening...' : isSpeaking ? 'Speaking...' : 'Ready to Learn'}
                </h2>
                <p className="text-white/60">
                  {isListening 
                    ? 'Speak your question or topic'
                    : isSpeaking 
                    ? 'AI is explaining the concept'
                    : 'Press and hold to start voice conversation'
                  }
                </p>
              </div>

              {/* Audio Waveform Visualization */}
              <div className="flex items-center justify-center space-x-1 mb-8 h-16">
                {audioWaveform.map((height, index) => (
                  <div
                    key={index}
                    className="w-2 bg-gradient-to-t from-purple-400 to-blue-400 rounded-full transition-all duration-100"
                    style={{ height: `${Math.max(4, height)}%` }}
                  />
                ))}
              </div>

              {/* Voice Controls */}
              <div className="flex items-center justify-center space-x-4">
                <button
                  onMouseDown={startListening}
                  disabled={isSpeaking}
                  className={`p-4 rounded-full transition-all duration-200 ${
                    isListening
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white disabled:opacity-50'
                  }`}
                >
                  {isListening ? <MicOff className="w-6 h-6" /> : <Mic className="w-6 h-6" />}
                </button>
              </div>
            </div>
          )}

          {/* Voice Settings */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h3 className="text-lg font-bold text-white mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Voice Settings
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-white/60 text-sm mb-2">Voice Type</label>
                <select 
                  value={voiceSettings.voice}
                  onChange={(e) => setVoiceSettings({...voiceSettings, voice: e.target.value})}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                >
                  <option value="female">Female</option>
                  <option value="male">Male</option>
                </select>
              </div>

              <div>
                <label className="block text-white/60 text-sm mb-2">Language</label>
                <select 
                  value={voiceSettings.language}
                  onChange={(e) => setVoiceSettings({...voiceSettings, language: e.target.value})}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                >
                  <option value="en-US">English (US)</option>
                  <option value="en-GB">English (UK)</option>
                  <option value="es-ES">Spanish</option>
                  <option value="fr-FR">French</option>
                </select>
              </div>

              <div>
                <label className="block text-white/60 text-sm mb-2">Speech Rate</label>
                <input
                  type="range"
                  min="0.5"
                  max="2"
                  step="0.1"
                  value={voiceSettings.rate}
                  onChange={(e) => setVoiceSettings({...voiceSettings, rate: parseFloat(e.target.value)})}
                  className="w-full"
                />
                <span className="text-white/60 text-sm">{voiceSettings.rate}x</span>
              </div>

              <div>
                <label className="block text-white/60 text-sm mb-2">Pitch</label>
                <input
                  type="range"
                  min="0.5"
                  max="2"
                  step="0.1"
                  value={voiceSettings.pitch}
                  onChange={(e) => setVoiceSettings({...voiceSettings, pitch: parseFloat(e.target.value)})}
                  className="w-full"
                />
                <span className="text-white/60 text-sm">{voiceSettings.pitch}x</span>
              </div>
            </div>
          </div>
        </div>

        {/* Session List */}
        <div className="space-y-6">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h3 className="text-lg font-bold text-white mb-4 flex items-center">
              <BookOpen className="w-5 h-5 mr-2" />
              Voice Sessions
            </h3>

            <div className="space-y-3">
              {voiceSessions.map((session) => (
                <div
                  key={session.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                    session.isActive
                      ? 'bg-purple-500/20 border-purple-400/50'
                      : 'bg-white/10 border-white/20 hover:bg-white/20'
                  }`}
                  onClick={() => startSession(session)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-medium text-sm">{session.title}</h4>
                    <span className="text-white/60 text-xs">{session.duration}</span>
                  </div>
                  <p className="text-white/60 text-xs mb-2">{session.subject}</p>
                  
                  {session.progress > 0 && (
                    <div className="w-full bg-white/20 rounded-full h-1">
                      <div
                        className="bg-gradient-to-r from-purple-400 to-blue-400 h-1 rounded-full"
                        style={{ width: `${session.progress}%` }}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Voice Learning Tips */}
          <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl p-6 border border-purple-400/20">
            <h3 className="text-lg font-bold text-white mb-4">Voice Learning Tips</h3>
            <div className="space-y-3 text-sm text-white/70">
              <p>• Speak clearly and at a normal pace</p>
              <p>• Use headphones for better audio quality</p>
              <p>• Ask questions anytime during the session</p>
              <p>• The AI adapts to your speaking style</p>
              <p>• Sessions are personalized to your level</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};