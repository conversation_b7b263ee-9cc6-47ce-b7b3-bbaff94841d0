import React from 'react';
import { 
  Home, 
  MessageCircle, 
  BookOpen, 
  Mic, 
  PenTool, 
  Trophy, 
  User, 
  Settings as SettingsIcon,
  GraduationCap,
  Zap
} from 'lucide-react';
import { ViewType } from '../App';

interface SidebarProps {
  currentView: ViewType;
  setCurrentView: (view: ViewType) => void;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  user: any;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  currentView, 
  setCurrentView, 
  sidebarOpen, 
  setSidebarOpen,
  user 
}) => {
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'chat', label: 'AI Tutor', icon: MessageCircle },
    { id: 'modules', label: 'Learning Modules', icon: BookOpen },
    { id: 'voice', label: 'Voice Learning', icon: Mic },
    { id: 'handwriting', label: 'Handwriting AI', icon: PenTool },
    { id: 'gamification', label: 'Achievements', icon: Trophy },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'settings', label: 'Settings', icon: SettingsIcon },
  ];

  return (
    <>
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      <div className={`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white/10 backdrop-blur-md border-r border-white/20
        transform transition-transform duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-8">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center">
                <GraduationCap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">EduAI</h1>
                <p className="text-sm text-white/60">Smart Tutoring</p>
              </div>
            </div>

            <div className="bg-white/10 rounded-xl p-4 mb-6">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-green-400 to-blue-400 flex items-center justify-center">
                  <span className="text-sm font-bold text-white">{user.name.charAt(0)}</span>
                </div>
                <div>
                  <p className="text-white font-medium text-sm">{user.name}</p>
                  <p className="text-white/60 text-xs">Level {user.level}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/80 text-xs">XP Progress</span>
                  <span className="text-white text-xs font-medium">{user.xp}/3000</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-orange-400 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(user.xp / 3000) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <nav className="flex-1 px-4">
            <ul className="space-y-2">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentView === item.id;
                
                return (
                  <li key={item.id}>
                    <button
                      onClick={() => {
                        setCurrentView(item.id as ViewType);
                        setSidebarOpen(false);
                      }}
                      className={`
                        w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200
                        ${isActive 
                          ? 'bg-white/20 text-white shadow-lg' 
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                        }
                      `}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{item.label}</span>
                      {isActive && (
                        <div className="ml-auto w-2 h-2 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full"></div>
                      )}
                    </button>
                  </li>
                );
              })}
            </ul>
          </nav>

          <div className="p-4">
            <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl p-4 border border-purple-400/20">
              <div className="flex items-center space-x-2 mb-2">
                <Zap className="w-4 h-4 text-yellow-400" />
                <span className="text-white text-sm font-medium">AI Boost</span>
              </div>
              <p className="text-white/70 text-xs mb-3">Unlock advanced AI features</p>
              <button className="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm py-2 px-4 rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200">
                Upgrade Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};