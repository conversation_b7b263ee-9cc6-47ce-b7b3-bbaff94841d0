import React, { useState } from 'react';
import { 
  Settings as SettingsIcon,
  Bell,
  Lock,
  Globe,
  Palette,
  Volume2,
  Shield,
  Download,
  Trash2,
  Eye,
  EyeOff,
  Check,
  X
} from 'lucide-react';

export const Settings: React.FC = () => {
  const [settings, setSettings] = useState({
    notifications: {
      dailyReminders: true,
      achievementAlerts: true,
      weeklyProgress: false,
      emailUpdates: true,
    },
    privacy: {
      profileVisibility: 'friends',
      dataSharing: false,
      analyticsTracking: true,
      leaderboardParticipation: true,
    },
    appearance: {
      theme: 'dark',
      language: 'en',
      fontSize: 'medium',
      animationsEnabled: true,
    },
    audio: {
      masterVolume: 80,
      speechRate: 1.0,
      voiceType: 'female',
      soundEffects: true,
    },
    security: {
      twoFactorAuth: false,
      loginAlerts: true,
      passwordStrength: 'strong',
    }
  });

  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [passwords, setPasswords] = useState({
    current: '',
    new: '',
    confirm: ''
  });

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handlePasswordChange = () => {
    // Simulate password change
    setShowPasswordChange(false);
    setPasswords({ current: '', new: '', confirm: '' });
  };

  const exportData = () => {
    // Simulate data export
    alert('Your learning data has been exported successfully!');
  };

  const deleteAccount = () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      alert('Account deletion request submitted. You will receive a confirmation email.');
    }
  };

  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-4 flex items-center">
            <SettingsIcon className="w-8 h-8 mr-3" />
            Settings
          </h1>
          <p className="text-white/70 text-lg">
            Customize your learning experience and manage your account
          </p>
        </div>

        <div className="space-y-6">
          {/* Notifications */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <Bell className="w-5 h-5 mr-2" />
              Notifications
            </h2>
            
            <div className="space-y-4">
              {[
                { key: 'dailyReminders', label: 'Daily learning reminders', description: 'Get notified about your daily learning goals' },
                { key: 'achievementAlerts', label: 'Achievement alerts', description: 'Receive notifications when you unlock new achievements' },
                { key: 'weeklyProgress', label: 'Weekly progress reports', description: 'Get a summary of your learning progress each week' },
                { key: 'emailUpdates', label: 'Email updates', description: 'Receive important updates and tips via email' },
              ].map((item) => (
                <div key={item.key} className="flex items-center justify-between p-3 bg-white/10 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{item.label}</p>
                    <p className="text-white/60 text-sm">{item.description}</p>
                  </div>
                  <button
                    onClick={() => updateSetting('notifications', item.key, !settings.notifications[item.key])}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.notifications[item.key] ? 'bg-purple-500' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.notifications[item.key] ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Privacy & Security */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <Shield className="w-5 h-5 mr-2" />
              Privacy & Security
            </h2>
            
            <div className="space-y-4">
              <div className="p-3 bg-white/10 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-white font-medium">Profile visibility</p>
                  <select
                    value={settings.privacy.profileVisibility}
                    onChange={(e) => updateSetting('privacy', 'profileVisibility', e.target.value)}
                    className="bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
                  >
                    <option value="public">Public</option>
                    <option value="friends">Friends only</option>
                    <option value="private">Private</option>
                  </select>
                </div>
                <p className="text-white/60 text-sm">Control who can see your profile and learning progress</p>
              </div>

              <div className="p-3 bg-white/10 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-white font-medium">Two-factor authentication</p>
                  <button
                    onClick={() => updateSetting('security', 'twoFactorAuth', !settings.security.twoFactorAuth)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.security.twoFactorAuth ? 'bg-purple-500' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.security.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
                <p className="text-white/60 text-sm">Add an extra layer of security to your account</p>
              </div>

              <div className="p-3 bg-white/10 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-white font-medium">Change password</p>
                  <button
                    onClick={() => setShowPasswordChange(!showPasswordChange)}
                    className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded-lg text-sm transition-colors"
                  >
                    {showPasswordChange ? 'Cancel' : 'Change'}
                  </button>
                </div>
                
                {showPasswordChange && (
                  <div className="mt-3 space-y-3">
                    <input
                      type="password"
                      placeholder="Current password"
                      value={passwords.current}
                      onChange={(e) => setPasswords({...passwords, current: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                    <input
                      type="password"
                      placeholder="New password"
                      value={passwords.new}
                      onChange={(e) => setPasswords({...passwords, new: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                    <input
                      type="password"
                      placeholder="Confirm new password"
                      value={passwords.confirm}
                      onChange={(e) => setPasswords({...passwords, confirm: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                    <div className="flex space-x-2">
                      <button
                        onClick={handlePasswordChange}
                        className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center space-x-1"
                      >
                        <Check className="w-4 h-4" />
                        <span>Update</span>
                      </button>
                      <button
                        onClick={() => setShowPasswordChange(false)}
                        className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center space-x-1"
                      >
                        <X className="w-4 h-4" />
                        <span>Cancel</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Appearance */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <Palette className="w-5 h-5 mr-2" />
              Appearance
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 bg-white/10 rounded-lg">
                <label className="block text-white font-medium mb-2">Theme</label>
                <select
                  value={settings.appearance.theme}
                  onChange={(e) => updateSetting('appearance', 'theme', e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                >
                  <option value="dark">Dark</option>
                  <option value="light">Light</option>
                  <option value="auto">Auto</option>
                </select>
              </div>

              <div className="p-3 bg-white/10 rounded-lg">
                <label className="block text-white font-medium mb-2">Language</label>
                <select
                  value={settings.appearance.language}
                  onChange={(e) => updateSetting('appearance', 'language', e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                </select>
              </div>

              <div className="p-3 bg-white/10 rounded-lg">
                <label className="block text-white font-medium mb-2">Font size</label>
                <select
                  value={settings.appearance.fontSize}
                  onChange={(e) => updateSetting('appearance', 'fontSize', e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>

              <div className="p-3 bg-white/10 rounded-lg">
                <div className="flex items-center justify-between">
                  <label className="text-white font-medium">Animations</label>
                  <button
                    onClick={() => updateSetting('appearance', 'animationsEnabled', !settings.appearance.animationsEnabled)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.appearance.animationsEnabled ? 'bg-purple-500' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.appearance.animationsEnabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Audio Settings */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <Volume2 className="w-5 h-5 mr-2" />
              Audio Settings
            </h2>
            
            <div className="space-y-4">
              <div className="p-3 bg-white/10 rounded-lg">
                <label className="block text-white font-medium mb-2">Master volume</label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.audio.masterVolume}
                  onChange={(e) => updateSetting('audio', 'masterVolume', parseInt(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-white/60 text-sm mt-1">
                  <span>0%</span>
                  <span>{settings.audio.masterVolume}%</span>
                  <span>100%</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-3 bg-white/10 rounded-lg">
                  <label className="block text-white font-medium mb-2">Speech rate</label>
                  <select
                    value={settings.audio.speechRate}
                    onChange={(e) => updateSetting('audio', 'speechRate', parseFloat(e.target.value))}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                  >
                    <option value={0.5}>0.5x</option>
                    <option value={0.75}>0.75x</option>
                    <option value={1.0}>1.0x</option>
                    <option value={1.25}>1.25x</option>
                    <option value={1.5}>1.5x</option>
                  </select>
                </div>

                <div className="p-3 bg-white/10 rounded-lg">
                  <label className="block text-white font-medium mb-2">Voice type</label>
                  <select
                    value={settings.audio.voiceType}
                    onChange={(e) => updateSetting('audio', 'voiceType', e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-400"
                  >
                    <option value="female">Female</option>
                    <option value="male">Male</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Data Management */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <Download className="w-5 h-5 mr-2" />
              Data Management
            </h2>
            
            <div className="space-y-4">
              <div className="p-3 bg-white/10 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white font-medium">Export your data</p>
                    <p className="text-white/60 text-sm">Download all your learning data and progress</p>
                  </div>
                  <button
                    onClick={exportData}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                  >
                    Export
                  </button>
                </div>
              </div>

              <div className="p-3 bg-red-500/20 border border-red-400/30 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-400 font-medium">Delete account</p>
                    <p className="text-white/60 text-sm">Permanently delete your account and all data</p>
                  </div>
                  <button
                    onClick={deleteAccount}
                    className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center space-x-1"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Delete</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};