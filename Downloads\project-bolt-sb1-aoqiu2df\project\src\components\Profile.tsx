import React, { useState } from 'react';
import { 
  User, 
  Mail, 
  Calendar, 
  MapPin, 
  Settings, 
  Camera,
  Book,
  TrendingUp,
  Target,
  Clock,
  Edit2,
  Save,
  X
} from 'lucide-react';

interface ProfileProps {
  user: any;
  setUser: (user: any) => void;
}

export const Profile: React.FC<ProfileProps> = ({ user, setUser }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState(user);

  const learningStyles = ['Visual', 'Auditory', 'Kinesthetic', 'Reading/Writing'];
  const subjects = ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'Literature', 'History'];

  const handleSave = () => {
    setUser(editedUser);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedUser(user);
    setIsEditing(false);
  };

  const stats = [
    { label: 'Total Study Time', value: '124 hours', icon: Clock, color: 'from-blue-400 to-purple-400' },
    { label: 'Lessons Completed', value: '89', icon: Book, color: 'from-green-400 to-blue-400' },
    { label: 'Current Level', value: user.level, icon: TrendingUp, color: 'from-yellow-400 to-orange-400' },
    { label: 'Success Rate', value: '94%', icon: Target, color: 'from-purple-400 to-pink-400' },
  ];

  const recentActivity = [
    { date: '2024-01-15', activity: 'Completed Calculus Module', xp: 150 },
    { date: '2024-01-14', activity: 'Achieved "Streak Master" badge', xp: 200 },
    { date: '2024-01-13', activity: 'Solved 25 Physics problems', xp: 120 },
    { date: '2024-01-12', activity: 'Voice learning session completed', xp: 100 },
    { date: '2024-01-11', activity: 'Chemistry quiz - Perfect score', xp: 180 },
  ];

  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-white">Profile</h1>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
          >
            <Edit2 className="w-4 h-4" />
            <span>{isEditing ? 'Cancel' : 'Edit Profile'}</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Info */}
          <div className="lg:col-span-1">
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 space-y-6">
              {/* Avatar */}
              <div className="text-center">
                <div className="relative inline-block mb-4">
                  <div className="w-24 h-24 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center">
                    <span className="text-3xl font-bold text-white">{user.name.charAt(0)}</span>
                  </div>
                  {isEditing && (
                    <button className="absolute bottom-0 right-0 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white hover:bg-purple-600 transition-colors">
                      <Camera className="w-4 h-4" />
                    </button>
                  )}
                </div>
                
                {isEditing ? (
                  <input
                    type="text"
                    value={editedUser.name}
                    onChange={(e) => setEditedUser({...editedUser, name: e.target.value})}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-center font-bold text-xl focus:outline-none focus:ring-2 focus:ring-purple-400"
                  />
                ) : (
                  <h2 className="text-xl font-bold text-white">{user.name}</h2>
                )}
                
                <p className="text-white/60 text-sm mt-1">Level {user.level} Learner</p>
              </div>

              {/* Basic Info */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="w-5 h-5 text-white/60" />
                  {isEditing ? (
                    <input
                      type="email"
                      value={editedUser.email || '<EMAIL>'}
                      onChange={(e) => setEditedUser({...editedUser, email: e.target.value})}
                      className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                  ) : (
                    <span className="text-white/80"><EMAIL></span>
                  )}
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-white/60" />
                  <span className="text-white/80">Joined January 2024</span>
                </div>

                <div className="flex items-center space-x-3">
                  <MapPin className="w-5 h-5 text-white/60" />
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedUser.location || 'New York, USA'}
                      onChange={(e) => setEditedUser({...editedUser, location: e.target.value})}
                      className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
                    />
                  ) : (
                    <span className="text-white/80">New York, USA</span>
                  )}
                </div>
              </div>

              {/* Learning Preferences */}
              <div className="border-t border-white/20 pt-4">
                <h3 className="text-white font-semibold mb-3">Learning Preferences</h3>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-white/60 text-sm mb-2">Preferred Learning Style</label>
                    {isEditing ? (
                      <select
                        value={editedUser.learningStyle}
                        onChange={(e) => setEditedUser({...editedUser, learningStyle: e.target.value})}
                        className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
                      >
                        {learningStyles.map(style => (
                          <option key={style} value={style}>{style}</option>
                        ))}
                      </select>
                    ) : (
                      <span className="text-white bg-purple-500/20 px-3 py-1 rounded-full text-sm">
                        {user.learningStyle}
                      </span>
                    )}
                  </div>

                  <div>
                    <label className="block text-white/60 text-sm mb-2">Favorite Subjects</label>
                    {isEditing ? (
                      <div className="space-y-2">
                        {subjects.map(subject => (
                          <label key={subject} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={editedUser.subjects.includes(subject)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setEditedUser({
                                    ...editedUser,
                                    subjects: [...editedUser.subjects, subject]
                                  });
                                } else {
                                  setEditedUser({
                                    ...editedUser,
                                    subjects: editedUser.subjects.filter(s => s !== subject)
                                  });
                                }
                              }}
                              className="rounded border-white/20 bg-white/10 text-purple-500 focus:ring-purple-400"
                            />
                            <span className="text-white text-sm">{subject}</span>
                          </label>
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-2">
                        {user.subjects.map(subject => (
                          <span key={subject} className="text-white bg-blue-500/20 px-2 py-1 rounded-full text-xs">
                            {subject}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {isEditing && (
                <div className="flex space-x-2 pt-4 border-t border-white/20">
                  <button
                    onClick={handleSave}
                    className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>Save</span>
                  </button>
                  <button
                    onClick={handleCancel}
                    className="flex-1 bg-white/10 hover:bg-white/20 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                  >
                    <X className="w-4 h-4" />
                    <span>Cancel</span>
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Stats and Activity */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {stats.map((stat, index) => (
                <div key={index} className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white/60 text-sm">{stat.label}</p>
                      <p className="text-2xl font-bold text-white">{stat.value}</p>
                    </div>
                    <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${stat.color} flex items-center justify-center`}>
                      <stat.icon className="w-5 h-5 text-white" />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Progress Chart */}
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">Learning Progress</h3>
              
              <div className="space-y-4">
                {user.subjects.map((subject, index) => {
                  const progress = [85, 92, 78][index] || 80;
                  return (
                    <div key={subject}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-white/80 text-sm">{subject}</span>
                        <span className="text-white text-sm font-medium">{progress}%</span>
                      </div>
                      <div className="w-full bg-white/20 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4">Recent Activity</h3>
              
              <div className="space-y-3">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/10 rounded-lg">
                    <div>
                      <p className="text-white text-sm font-medium">{activity.activity}</p>
                      <p className="text-white/60 text-xs">{activity.date}</p>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-yellow-400 text-sm font-medium">+{activity.xp}</span>
                      <span className="text-yellow-400 text-xs">XP</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};