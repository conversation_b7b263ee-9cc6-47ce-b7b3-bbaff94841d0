import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Atom, 
  Beaker, 
  Globe, 
  Book, 
  Languages,
  Play,
  Lock,
  CheckCircle,
  Star,
  Clock,
  Users,
  TrendingUp
} from 'lucide-react';

interface Module {
  id: string;
  title: string;
  description: string;
  icon: any;
  color: string;
  progress: number;
  lessons: number;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  duration: string;
  students: number;
  rating: number;
  isLocked: boolean;
  status: 'not-started' | 'in-progress' | 'completed';
}

export const LearningModules: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedModule, setSelectedModule] = useState<Module | null>(null);

  const modules: Module[] = [
    {
      id: 'calculus',
      title: 'Advanced Calculus',
      description: 'Master derivatives, integrals, and their applications with AI-powered adaptive learning.',
      icon: Calculator,
      color: 'from-blue-400 to-purple-400',
      progress: 65,
      lessons: 24,
      difficulty: 'Advanced',
      duration: '8 weeks',
      students: 15420,
      rating: 4.9,
      isLocked: false,
      status: 'in-progress'
    },
    {
      id: 'quantum-physics',
      title: 'Quantum Physics',
      description: 'Explore the fascinating world of quantum mechanics and particle physics.',
      icon: Atom,
      color: 'from-purple-400 to-pink-400',
      progress: 30,
      lessons: 18,
      difficulty: 'Advanced',
      duration: '6 weeks',
      students: 8930,
      rating: 4.8,
      isLocked: false,
      status: 'in-progress'
    },
    {
      id: 'organic-chemistry',
      title: 'Organic Chemistry',
      description: 'Understand molecular structures, reactions, and mechanisms.',
      icon: Beaker,
      color: 'from-green-400 to-blue-400',
      progress: 100,
      lessons: 20,
      difficulty: 'Intermediate',
      duration: '7 weeks',
      students: 12300,
      rating: 4.7,
      isLocked: false,
      status: 'completed'
    },
    {
      id: 'linear-algebra',
      title: 'Linear Algebra',
      description: 'Vectors, matrices, and linear transformations made simple.',
      icon: Calculator,
      color: 'from-orange-400 to-red-400',
      progress: 0,
      lessons: 16,
      difficulty: 'Intermediate',
      duration: '5 weeks',
      students: 18750,
      rating: 4.9,
      isLocked: false,
      status: 'not-started'
    },
    {
      id: 'astrophysics',
      title: 'Astrophysics',
      description: 'Journey through the cosmos and understand stellar evolution.',
      icon: Globe,
      color: 'from-indigo-400 to-purple-400',
      progress: 0,
      lessons: 22,
      difficulty: 'Advanced',
      duration: '10 weeks',
      students: 6420,
      rating: 4.8,
      isLocked: true,
      status: 'not-started'
    },
    {
      id: 'literature',
      title: 'World Literature',
      description: 'Analyze great works of literature with AI-powered insights.',
      icon: Book,
      color: 'from-yellow-400 to-orange-400',
      progress: 0,
      lessons: 15,
      difficulty: 'Beginner',
      duration: '4 weeks',
      students: 9840,
      rating: 4.6,
      isLocked: true,
      status: 'not-started'
    }
  ];

  const categories = [
    { id: 'all', label: 'All Subjects' },
    { id: 'mathematics', label: 'Mathematics' },
    { id: 'physics', label: 'Physics' },
    { id: 'chemistry', label: 'Chemistry' },
    { id: 'literature', label: 'Literature' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'in-progress':
        return <Play className="w-5 h-5 text-blue-400" />;
      default:
        return <Play className="w-5 h-5 text-white/60" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-500/20 text-green-400';
      case 'Intermediate':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'Advanced':
        return 'bg-red-500/20 text-red-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-4">AI-Powered Learning Modules</h1>
        <p className="text-white/70 text-lg">
          Personalized learning paths adapted to your pace and style
        </p>
      </div>

      {/* Category Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                selectedCategory === category.id
                  ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white'
                  : 'bg-white/10 text-white/70 hover:bg-white/20 hover:text-white'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>
      </div>

      {/* Learning Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        {[
          { label: 'Modules Completed', value: '12', icon: CheckCircle, color: 'from-green-400 to-blue-400' },
          { label: 'In Progress', value: '3', icon: Play, color: 'from-blue-400 to-purple-400' },
          { label: 'Total Study Time', value: '124h', icon: Clock, color: 'from-orange-400 to-red-400' },
          { label: 'Avg. Performance', value: '89%', icon: TrendingUp, color: 'from-yellow-400 to-orange-400' },
        ].map((stat, index) => (
          <div key={index} className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">{stat.label}</p>
                <p className="text-2xl font-bold text-white">{stat.value}</p>
              </div>
              <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${stat.color} flex items-center justify-center`}>
                <stat.icon className="w-5 h-5 text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modules Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {modules.map((module) => (
          <div
            key={module.id}
            className={`bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 cursor-pointer relative overflow-hidden group ${
              module.isLocked ? 'opacity-60' : ''
            }`}
            onClick={() => !module.isLocked && setSelectedModule(module)}
          >
            {module.isLocked && (
              <div className="absolute top-4 right-4 z-10">
                <Lock className="w-5 h-5 text-white/60" />
              </div>
            )}

            {/* Background Gradient */}
            <div className={`absolute inset-0 bg-gradient-to-br ${module.color} opacity-10 group-hover:opacity-20 transition-opacity duration-300`} />

            {/* Content */}
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${module.color} flex items-center justify-center`}>
                  <module.icon className="w-6 h-6 text-white" />
                </div>
                {getStatusIcon(module.status)}
              </div>

              <h3 className="text-xl font-bold text-white mb-2">{module.title}</h3>
              <p className="text-white/70 text-sm mb-4 line-clamp-2">{module.description}</p>

              {/* Progress Bar */}
              {module.progress > 0 && (
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/60 text-sm">Progress</span>
                    <span className="text-white text-sm font-medium">{module.progress}%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className={`bg-gradient-to-r ${module.color} h-2 rounded-full transition-all duration-300`}
                      style={{ width: `${module.progress}%` }}
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between text-sm text-white/60 mb-4">
                <div className="flex items-center space-x-4">
                  <span>{module.lessons} lessons</span>
                  <span>{module.duration}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-white">{module.rating}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(module.difficulty)}`}>
                  {module.difficulty}
                </span>
                <div className="flex items-center space-x-1 text-white/60">
                  <Users className="w-4 h-4" />
                  <span className="text-xs">{module.students.toLocaleString()}</span>
                </div>
              </div>

              {!module.isLocked && (
                <button
                  className={`w-full mt-4 bg-gradient-to-r ${module.color} text-white py-3 px-4 rounded-lg font-medium hover:opacity-90 transition-opacity`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedModule(module);
                  }}
                >
                  {module.status === 'completed' ? 'Review' : module.status === 'in-progress' ? 'Continue' : 'Start Learning'}
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* AI Recommendations */}
      <div className="mt-12 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl p-6 border border-purple-400/20">
        <h2 className="text-xl font-bold text-white mb-4">AI Recommendations</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">Next Best Module</h3>
            <p className="text-white/70 text-sm">Based on your progress, Linear Algebra would complement your Calculus studies perfectly.</p>
          </div>
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">Optimal Study Time</h3>
            <p className="text-white/70 text-sm">Your performance peaks at 10 AM. Schedule complex topics during this time.</p>
          </div>
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">Learning Style</h3>
            <p className="text-white/70 text-sm">Visual learner detected. Modules with diagrams and animations are recommended.</p>
          </div>
        </div>
      </div>
    </div>
  );
};