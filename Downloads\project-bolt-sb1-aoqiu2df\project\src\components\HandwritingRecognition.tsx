import React, { useState, useRef, useEffect } from 'react';
import { 
  PenTool, 
  Eraser, 
  RotateCcw, 
  Camera, 
  Upload, 
  CheckCircle,
  Brain,
  Calculator,
  Download,
  Lightbulb
} from 'lucide-react';

interface MathProblem {
  id: string;
  equation: string;
  solution: string;
  steps: string[];
  confidence: number;
  subject: 'math' | 'physics' | 'chemistry';
}

export const HandwritingRecognition: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [tool, setTool] = useState<'pen' | 'eraser'>('pen');
  const [recognizedText, setRecognizedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [mathProblem, setMathProblem] = useState<MathProblem | null>(null);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);

  const sampleProblems: MathProblem[] = [
    {
      id: '1',
      equation: '∫(x² + 2x + 1)dx',
      solution: '(x³/3) + x² + x + C',
      steps: [
        'Integrate each term separately',
        '∫x²dx = x³/3',
        '∫2x dx = x²',
        '∫1 dx = x',
        'Add constant of integration C'
      ],
      confidence: 0.94,
      subject: 'math'
    },
    {
      id: '2',
      equation: 'F = ma',
      solution: 'a = F/m',
      steps: [
        'Start with Newton\'s second law: F = ma',
        'Divide both sides by mass (m)',
        'Result: a = F/m',
        'Acceleration equals force divided by mass'
      ],
      confidence: 0.98,
      subject: 'physics'
    }
  ];

  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
      }
    }
  }, []);

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (canvas) {
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.beginPath();
        ctx.moveTo(x, y);
      }
    }
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
    
    const canvas = canvasRef.current;
    if (canvas) {
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        if (tool === 'pen') {
          ctx.globalCompositeOperation = 'source-over';
          ctx.strokeStyle = '#ffffff';
          ctx.lineWidth = 2;
        } else {
          ctx.globalCompositeOperation = 'destination-out';
          ctx.lineWidth = 10;
        }
        
        ctx.lineTo(x, y);
        ctx.stroke();
      }
    }
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
    setRecognizedText('');
    setMathProblem(null);
  };

  const recognizeHandwriting = async () => {
    setIsProcessing(true);
    
    // Simulate AI processing
    setTimeout(() => {
      const randomProblem = sampleProblems[Math.floor(Math.random() * sampleProblems.length)];
      setRecognizedText(randomProblem.equation);
      setMathProblem(randomProblem);
      setIsProcessing(false);
    }, 2000);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setUploadedImage(result);
        
        // Simulate processing uploaded image
        setIsProcessing(true);
        setTimeout(() => {
          const randomProblem = sampleProblems[Math.floor(Math.random() * sampleProblems.length)];
          setRecognizedText(randomProblem.equation);
          setMathProblem(randomProblem);
          setIsProcessing(false);
        }, 2000);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="p-6 h-full overflow-y-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-4 flex items-center">
          <PenTool className="w-8 h-8 mr-3" />
          AI Handwriting Recognition
        </h1>
        <p className="text-white/70 text-lg">
          Write math, physics, or chemistry problems and get instant AI-powered solutions
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Drawing Canvas */}
        <div className="space-y-6">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white">Write Your Problem</h2>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setTool('pen')}
                  className={`p-2 rounded-lg transition-colors ${
                    tool === 'pen' ? 'bg-purple-500 text-white' : 'bg-white/10 text-white/70 hover:bg-white/20'
                  }`}
                >
                  <PenTool className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setTool('eraser')}
                  className={`p-2 rounded-lg transition-colors ${
                    tool === 'eraser' ? 'bg-purple-500 text-white' : 'bg-white/10 text-white/70 hover:bg-white/20'
                  }`}
                >
                  <Eraser className="w-5 h-5" />
                </button>
                <button
                  onClick={clearCanvas}
                  className="p-2 rounded-lg bg-white/10 text-white/70 hover:bg-white/20 transition-colors"
                >
                  <RotateCcw className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="relative">
              <canvas
                ref={canvasRef}
                width={400}
                height={300}
                className="w-full h-72 bg-white/5 rounded-lg border-2 border-dashed border-white/20 cursor-crosshair"
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={stopDrawing}
                onMouseLeave={stopDrawing}
              />
              
              {!isDrawing && (
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <p className="text-white/60 text-sm">Draw your equation here</p>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-4 mt-4">
              <button
                onClick={recognizeHandwriting}
                disabled={isProcessing}
                className="flex-1 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 text-white py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2"
              >
                {isProcessing ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <Brain className="w-5 h-5" />
                    <span>Recognize & Solve</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Image Upload */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h3 className="text-lg font-bold text-white mb-4 flex items-center">
              <Camera className="w-5 h-5 mr-2" />
              Upload Image
            </h3>
            
            <div className="space-y-4">
              <div className="border-2 border-dashed border-white/20 rounded-lg p-6 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="cursor-pointer flex flex-col items-center space-y-2"
                >
                  <Upload className="w-8 h-8 text-white/60" />
                  <p className="text-white/60">Upload handwritten problem</p>
                  <p className="text-white/40 text-sm">PNG, JPG up to 5MB</p>
                </label>
              </div>

              {uploadedImage && (
                <div className="rounded-lg overflow-hidden">
                  <img
                    src={uploadedImage}
                    alt="Uploaded problem"
                    className="w-full h-32 object-cover"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="space-y-6">
          {/* Recognition Result */}
          {recognizedText && (
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-white">Recognized Text</h2>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-green-400 text-sm font-medium">
                    {mathProblem ? `${(mathProblem.confidence * 100).toFixed(0)}% confident` : ''}
                  </span>
                </div>
              </div>
              
              <div className="bg-white/10 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-center">
                  <span className="text-2xl font-mono text-white">{recognizedText}</span>
                </div>
              </div>
            </div>
          )}

          {/* Solution */}
          {mathProblem && (
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-4 flex items-center">
                <Calculator className="w-5 h-5 mr-2" />
                AI-Generated Solution
              </h2>
              
              <div className="space-y-4">
                <div className="bg-green-500/20 border border-green-400/30 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-green-400 font-medium">Solution:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      mathProblem.subject === 'math' ? 'bg-blue-500/20 text-blue-400' :
                      mathProblem.subject === 'physics' ? 'bg-purple-500/20 text-purple-400' :
                      'bg-orange-500/20 text-orange-400'
                    }`}>
                      {mathProblem.subject.charAt(0).toUpperCase() + mathProblem.subject.slice(1)}
                    </span>
                  </div>
                  <div className="text-xl font-mono text-white text-center">
                    {mathProblem.solution}
                  </div>
                </div>

                <div className="bg-white/10 rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-3 flex items-center">
                    <Lightbulb className="w-4 h-4 mr-2" />
                    Step-by-Step Solution:
                  </h3>
                  <div className="space-y-2">
                    {mathProblem.steps.map((step, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-white text-xs font-bold">{index + 1}</span>
                        </div>
                        <p className="text-white/80 text-sm leading-relaxed">{step}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button className="flex-1 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>Save Solution</span>
                  </button>
                  <button className="flex-1 bg-white/10 hover:bg-white/20 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                    Generate Similar
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* AI Tips */}
          <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl p-6 border border-purple-400/20">
            <h3 className="text-lg font-bold text-white mb-4">Recognition Tips</h3>
            <div className="space-y-3 text-sm text-white/70">
              <p>• Write clearly with good contrast</p>
              <p>• Use standard mathematical notation</p>
              <p>• Keep equations on a single line when possible</p>
              <p>• The AI recognizes equations, formulas, and diagrams</p>
              <p>• Supports math, physics, and chemistry problems</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};