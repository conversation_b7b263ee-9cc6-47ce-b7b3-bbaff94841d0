import React from 'react';
import { 
  TrendingUp, 
  Clock, 
  Target, 
  BookOpen, 
  Brain, 
  Zap,
  Calendar,
  Award
} from 'lucide-react';

interface DashboardProps {
  user: any;
}

export const Dashboard: React.FC<DashboardProps> = ({ user }) => {
  const todaySchedule = [
    { time: '09:00', subject: 'Mathematics', topic: 'Calculus Integration', duration: '45 min' },
    { time: '10:30', subject: 'Physics', topic: 'Quantum Mechanics', duration: '60 min' },
    { time: '14:00', subject: 'Chemistry', topic: 'Organic Reactions', duration: '30 min' },
  ];

  const recentAchievements = [
    { title: 'Problem Solver', description: 'Solved 50 math problems', icon: '🧮' },
    { title: 'Streak Master', description: '10 days learning streak', icon: '🔥' },
    { title: 'Voice Learner', description: 'Completed voice lesson', icon: '🎙️' },
  ];

  const learningInsights = [
    { label: 'Best Learning Time', value: '10:00 AM', change: '+15%' },
    { label: 'Avg. Session Length', value: '42 min', change: '+8%' },
    { label: 'Retention Rate', value: '89%', change: '+12%' },
    { label: 'Weekly Progress', value: '94%', change: '+6%' },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            Good morning, {user.name}! 👋
          </h1>
          <p className="text-white/70">
            Ready to continue your learning journey? You're doing great!
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20">
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-purple-400" />
              <span className="text-white font-medium">Today</span>
            </div>
            <p className="text-2xl font-bold text-white mt-1">
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'short', 
                month: 'short', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          { label: 'Current Streak', value: `${user.streak} days`, icon: TrendingUp, color: 'from-orange-400 to-red-400' },
          { label: 'Study Time Today', value: '2h 15m', icon: Clock, color: 'from-blue-400 to-purple-400' },
          { label: 'Goals Completed', value: '7/10', icon: Target, color: 'from-green-400 to-blue-400' },
          { label: 'Current Level', value: `Level ${user.level}`, icon: Award, color: 'from-yellow-400 to-orange-400' },
        ].map((stat, index) => (
          <div key={index} className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${stat.color} flex items-center justify-center`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-white">{stat.value}</p>
                <p className="text-white/60 text-sm">{stat.label}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Today's Schedule */}
        <div className="lg:col-span-2 bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white flex items-center">
              <BookOpen className="w-5 h-5 mr-2" />
              Today's AI-Curated Schedule
            </h2>
            <button className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              Optimize Schedule
            </button>
          </div>
          
          <div className="space-y-4">
            {todaySchedule.map((session, index) => (
              <div key={index} className="bg-white/10 rounded-xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-400 to-blue-400 flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{session.time}</span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">{session.subject}</h3>
                      <p className="text-white/70 text-sm">{session.topic}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white/60 text-sm">{session.duration}</p>
                    <button className="bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded-lg text-xs mt-1 transition-colors">
                      Start
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* AI Learning Insights */}
        <div className="space-y-6">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <Brain className="w-5 h-5 mr-2" />
              AI Insights
            </h2>
            
            <div className="space-y-4">
              {learningInsights.map((insight, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-white/80 text-sm">{insight.label}</p>
                    <p className="text-white font-semibold">{insight.value}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400 text-sm font-medium">{insight.change}</span>
                    <TrendingUp className="w-4 h-4 text-green-400" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              Recent Achievements
            </h2>
            
            <div className="space-y-3">
              {recentAchievements.map((achievement, index) => (
                <div key={index} className="flex items-center space-x-3 bg-white/10 rounded-lg p-3">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div>
                    <p className="text-white font-medium text-sm">{achievement.title}</p>
                    <p className="text-white/60 text-xs">{achievement.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* AI Recommendations */}
      <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl p-6 border border-purple-400/20">
        <h2 className="text-xl font-bold text-white mb-4 flex items-center">
          <Brain className="w-5 h-5 mr-2" />
          AI Recommendations for You
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            {
              title: 'Focus on Calculus',
              description: 'Based on your recent performance, spend extra time on integration techniques.',
              action: 'Start Practice',
              color: 'from-blue-400 to-purple-400'
            },
            {
              title: 'Voice Learning Session',
              description: 'Your auditory learning style suggests voice sessions would be beneficial.',
              action: 'Try Voice Mode',
              color: 'from-green-400 to-blue-400'
            },
            {
              title: 'Take a Break',
              description: 'You\'ve been studying for 2 hours. A 10-minute break is recommended.',
              action: 'Set Reminder',
              color: 'from-orange-400 to-red-400'
            },
          ].map((rec, index) => (
            <div key={index} className="bg-white/10 rounded-xl p-4 border border-white/20">
              <h3 className="text-white font-semibold mb-2">{rec.title}</h3>
              <p className="text-white/70 text-sm mb-4">{rec.description}</p>
              <button className={`w-full bg-gradient-to-r ${rec.color} text-white py-2 px-4 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity`}>
                {rec.action}
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};