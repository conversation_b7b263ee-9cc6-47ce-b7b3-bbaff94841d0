import React, { useState, useEffect } from 'react';
import { Sidebar } from './components/Sidebar';
import { Dashboard } from './components/Dashboard';
import { ChatBot } from './components/ChatBot';
import { LearningModules } from './components/LearningModules';
import { VoiceLearning } from './components/VoiceLearning';
import { HandwritingRecognition } from './components/HandwritingRecognition';
import { Gamification } from './components/Gamification';
import { Profile } from './components/Profile';
import { Settings } from './components/Settings';

export type ViewType = 'dashboard' | 'chat' | 'modules' | 'voice' | 'handwriting' | 'gamification' | 'profile' | 'settings';

function App() {
  const [currentView, setCurrentView] = useState<ViewType>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user, setUser] = useState({
    name: '<PERSON>',
    level: 5,
    xp: 2340,
    streak: 12,
    learningStyle: 'Visual',
    subjects: ['Mathematics', 'Physics', 'Chemistry']
  });

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard user={user} />;
      case 'chat':
        return <ChatBot />;
      case 'modules':
        return <LearningModules />;
      case 'voice':
        return <VoiceLearning />;
      case 'handwriting':
        return <HandwritingRecognition />;
      case 'gamification':
        return <Gamification user={user} />;
      case 'profile':
        return <Profile user={user} setUser={setUser} />;
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard user={user} />;
    }
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Sidebar 
        currentView={currentView} 
        setCurrentView={setCurrentView}
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        user={user}
      />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white/10 backdrop-blur-md border-b border-white/20 px-6 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
            >
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 bg-white/10 rounded-full px-4 py-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-white text-sm font-medium">AI Assistant Active</span>
              </div>
              
              <div className="flex items-center space-x-2 text-white">
                <span className="text-sm">Streak: {user.streak} days</span>
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-400 to-red-400 flex items-center justify-center">
                  <span className="text-xs font-bold">🔥</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto">
          {renderCurrentView()}
        </main>
      </div>
    </div>
  );
}

export default App;